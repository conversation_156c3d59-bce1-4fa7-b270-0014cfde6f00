import { DashboardDivider } from '@g17eco/atoms/divider';
import { Button } from 'reactstrap';

export interface EditorTemplateProps {
  handleCancel: () => void;
  handleSubmit: () => void;
  isDisabled: boolean;
  children: React.ReactNode | (React.ReactNode | null)[];
}

export const EditorTemplate = ({ handleCancel, isDisabled, handleSubmit, children }: EditorTemplateProps) => {
  const actionButtons = (
    <div className='d-flex justify-content-between'>
      <Button color='link-secondary' onClick={handleCancel} className='mr-3'>
        Cancel
      </Button>
      <Button color='primary' disabled={isDisabled} onClick={handleSubmit}>
        Submit
      </Button>
    </div>
  );
  return (
    <>
      {actionButtons}
      <DashboardDivider className='mt-4' />
      {children}
      <DashboardDivider className='mt-4 mb-4' />
      {actionButtons}
    </>
  );
};
