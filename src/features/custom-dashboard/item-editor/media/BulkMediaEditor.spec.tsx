import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { renderWithProviders } from '../../../../__fixtures__/utils';
import { configureStore } from '@reduxjs/toolkit';
import { BulkMediaEditor } from './BulkMediaEditor';
import { BulkMediaEditorProps } from '../../types';
import { GridDashboardTempBulkMediaItem, InsightDashboardTempItemType } from '@g17eco/types/insight-custom-dashboard';
import { useMediaFileProcessor } from '../../hooks/useMediaFileProcessor';
import { generateObjectId } from '@utils/object-id';
import { FileDropZoneProps } from '@components/files/FileDropZone';
import { reducer } from '@reducers/index';
import { EditorTemplateProps } from '../EditorTemplate';
import { BulkMediaPreviewProps } from './BulkMediaPreview';
import { cleanupFileByUrl } from '@utils/files';

// Mock the custom hooks
vi.mock('../../hooks/useMediaFileProcessor', () => ({
  useMediaFileProcessor: vi.fn(),
}));
vi.mock('@utils/files', () => ({
  removeFileFromArray: vi.fn(),
  cleanupFileByUrl: vi.fn(),
}));
vi.mock('@utils/object-id', () => ({
  generateObjectId: vi.fn(),
}));

// Mock child components
vi.mock('../EditorTemplate', () => ({
  EditorTemplate: ({ children, isDisabled, handleCancel, handleSubmit }: EditorTemplateProps) => (
    <div data-testid='editor-template'>
      {children}
      <div>
        <button data-testid='cancel-button' onClick={handleCancel}>
          Cancel
        </button>
        <button data-testid='submit-button' onClick={handleSubmit} disabled={isDisabled}>
          Submit
        </button>
      </div>
    </div>
  ),
}));

vi.mock('@g17eco/atoms/divider', () => ({
  DashboardDivider: ({ className }: { className: string }) => (
    <div data-testid='dashboard-divider' className={className} />
  ),
}));

vi.mock('@components/files/FileDropZone', () => ({
  FileDropZone: ({ children, onDrop, multiple, maxSize, accept }: FileDropZoneProps) => (
    <div
      data-testid='file-drop-zone'
      data-multiple={multiple}
      data-max-size={maxSize}
      data-accept={JSON.stringify(accept)}
      onClick={(e) => {
        // Simulate file drop for testing
        const mockFiles = [new File(['content'], 'test.jpg', { type: 'image/jpeg' })];
        const mockRejections: any[] = [];
        onDrop?.(mockFiles, mockRejections, e as any);
      }}
    >
      {children}
    </div>
  ),
}));

vi.mock('./DropZonePlaceholder', () => ({
  DropZonePlaceholder: () => <div data-testid='drop-zone-placeholder'>Drop files here</div>,
}));

vi.mock('./BulkMediaPreview', () => ({
  BulkMediaPreview: ({ title, file, handleClickDelete, handleChangeTitle }: BulkMediaPreviewProps) => (
    <div data-testid='bulk-media-preview'>
      <input
        data-testid='title-input'
        value={title || ''}
        onChange={(e) => handleChangeTitle(e.target.value)}
        placeholder='Enter title'
      />
      <button data-testid='delete-button' onClick={handleClickDelete}>
        Delete
      </button>
      <span data-testid='file-name'>{file.name}</span>
    </div>
  ),
}));

vi.mock('@g17eco/molecules/alert', () => ({
  BasicAlert: ({ children }: { children: React.ReactNode }) => <div data-testid='basic-alert'>{children}</div>,
}));

describe('BulkMediaEditor', () => {
  const mockProcessFiles = vi.fn();
  const mockHandleCancel = vi.fn();
  const mockUpdateItem = vi.fn();

  const defaultEditingItem: GridDashboardTempBulkMediaItem = {
    type: InsightDashboardTempItemType.BulkMedia,
  };

  const defaultProps: BulkMediaEditorProps = {
    editingItem: defaultEditingItem,
    handleCancel: mockHandleCancel,
    updateItem: mockUpdateItem,
  };

  const mockStore = configureStore({ reducer });

  beforeEach(() => {
    vi.clearAllMocks();

    // "vi.mocked" is a type helper to avoid casting as Mock, since
    // TypeScript doesn't know that generateObjectId is a mocked function
    vi.mocked(generateObjectId).mockReturnValue('mock-unique-id');

    vi.mocked(useMediaFileProcessor).mockReturnValue({
      error: undefined,
      processFiles: mockProcessFiles,
      clearError: vi.fn(),
    });

    mockProcessFiles.mockResolvedValue({
      mediaFiles: [
        {
          name: 'test.jpg',
          url: 'blob:mock-url',
          type: 'image/jpeg',
          ratio: 1.5,
          originalFile: new File(['content'], 'test.jpg', { type: 'image/jpeg' }),
        },
      ],
      error: undefined,
    });
  });

  describe('Component Initialization', () => {
    it('should render with empty state', () => {
      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      expect(screen.getByTestId('editor-template')).toBeInTheDocument();
      expect(screen.getByText('Upload media')).toBeInTheDocument();
      expect(screen.getByTestId('file-drop-zone')).toBeInTheDocument();
      expect(screen.getByTestId('drop-zone-placeholder')).toBeInTheDocument();
      expect(screen.queryByTestId('dashboard-divider')).not.toBeInTheDocument();
      expect(screen.queryByTestId('bulk-media-preview')).not.toBeInTheDocument();
    });

    it('should configure FileDropZone with correct props', () => {
      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      const fileDropZone = screen.getByTestId('file-drop-zone');
      expect(fileDropZone).toHaveAttribute('data-multiple', 'true');
      expect(fileDropZone).toHaveAttribute('data-max-size', '52428800'); // 50MB in bytes
    });
  });

  describe('File Upload Handling', () => {
    it('should handle file upload successfully', async () => {
      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      const fileDropZone = screen.getByTestId('file-drop-zone');
      fireEvent.click(fileDropZone);

      expect(mockProcessFiles).toHaveBeenCalledWith([expect.any(File)], []);
      expect(await screen.findByTestId('dashboard-divider')).toBeInTheDocument();
      expect(await screen.findByTestId('bulk-media-preview')).toBeInTheDocument();
    });

    it('should create media items with unique IDs when files are added', async () => {
      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      const fileDropZone = screen.getByTestId('file-drop-zone');
      fireEvent.click(fileDropZone);

      expect(await screen.findByTestId('bulk-media-preview')).toBeInTheDocument();
      // Verify that the component creates media items (the preview is rendered)
      expect(screen.getByTestId('file-name')).toHaveTextContent('test.jpg');
    });

    it('should handle multiple file uploads', async () => {
      const multipleFiles = [
        {
          name: 'file1.jpg',
          url: 'blob:mock-url-1',
          type: 'image/jpeg',
          ratio: 1.5,
          originalFile: new File(['content1'], 'file1.jpg', { type: 'image/jpeg' }),
        },
        {
          name: 'file2.png',
          url: 'blob:mock-url-2',
          type: 'image/png',
          ratio: 1.2,
          originalFile: new File(['content2'], 'file2.png', { type: 'image/png' }),
        },
      ];

      mockProcessFiles.mockResolvedValueOnce({
        mediaFiles: multipleFiles,
        error: undefined,
      });

      // Set up different IDs for each call
      vi.mocked(generateObjectId).mockReturnValueOnce('unique-id-1').mockReturnValueOnce('unique-id-2');

      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      const fileDropZone = screen.getByTestId('file-drop-zone');
      fireEvent.click(fileDropZone);

      expect(await screen.findAllByTestId('bulk-media-preview')).toHaveLength(2);
    });
  });

  describe('File Deletion', () => {
    it('should handle file deletion and cleanup', async () => {
      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      // First add a file
      const fileDropZone = screen.getByTestId('file-drop-zone');
      fireEvent.click(fileDropZone);

      expect(await screen.findByTestId('bulk-media-preview')).toBeInTheDocument();

      // Then delete it
      const deleteButton = screen.getByTestId('delete-button');
      fireEvent.click(deleteButton);

      expect(vi.mocked(cleanupFileByUrl)).toHaveBeenCalledWith('blob:mock-url');
      expect(screen.queryByTestId('bulk-media-preview')).not.toBeInTheDocument();
      expect(screen.queryByTestId('dashboard-divider')).not.toBeInTheDocument();
    });
  });

  describe('Title Change Functionality', () => {
    it('should handle title changes for media items', async () => {
      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      // Add a file first
      const fileDropZone = screen.getByTestId('file-drop-zone');
      fireEvent.click(fileDropZone);

      expect(await screen.findByTestId('bulk-media-preview')).toBeInTheDocument();

      // Change the title
      const titleInput = screen.getByTestId('title-input');
      fireEvent.change(titleInput, { target: { value: 'New Title' } });

      expect(titleInput).toHaveValue('New Title');
    });
  });

  describe('Submit Functionality', () => {
    it('should call updateItem with correct data structure on submit', async () => {
      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      // Add a file
      const fileDropZone = screen.getByTestId('file-drop-zone');
      fireEvent.click(fileDropZone);

      expect(await screen.findByTestId('bulk-media-preview')).toBeInTheDocument();

      // Submit
      const submitButton = screen.getByTestId('submit-button');
      expect(submitButton).not.toBeDisabled();
      fireEvent.click(submitButton);

      expect(mockUpdateItem).toHaveBeenCalledWith([
        expect.objectContaining({
          tempId: 'mock-unique-id',
          type: 'media',
          files: [
            expect.objectContaining({
              name: 'test.jpg',
              url: 'blob:mock-url',
              type: 'image/jpeg',
              ratio: 1.5,
            }),
          ],
        }),
      ]);
    });

    it('should render cancel button correctly', () => {
      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      const cancelButton = screen.getByTestId('cancel-button');
      expect(cancelButton).toBeInTheDocument();
      expect(cancelButton).toHaveTextContent('Cancel');

      // Verify the button is clickable (not disabled)
      expect(cancelButton).not.toBeDisabled();
    });
  });

  describe('Disabled State Management', () => {
    it('should be disabled when no files are present', () => {
      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      const submitButton = screen.getByTestId('submit-button');
      expect(submitButton).toBeDisabled();
    });

    it('should be disabled when max size is exceeded', async () => {
      // Mock a large file that exceeds MAX_SIZE
      const largeFile = {
        name: 'large-file.jpg',
        url: 'blob:mock-url',
        type: 'image/jpeg',
        ratio: 1.5,
        originalFile: new File(['x'.repeat(60 * 1024 * 1024)], 'large-file.jpg', { type: 'image/jpeg' }),
      };

      mockProcessFiles.mockResolvedValueOnce({
        mediaFiles: [largeFile],
        error: undefined,
      });

      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      const fileDropZone = screen.getByTestId('file-drop-zone');
      fireEvent.click(fileDropZone);

      expect(await screen.findByTestId('submit-button')).toBeDisabled();

      // Should show max size error
      const alerts = await screen.findAllByTestId('basic-alert');
      const maxSizeAlert = alerts.find((alert) => alert.textContent?.includes('Total size of files is larger than'));
      expect(maxSizeAlert).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should display error from file processing', async () => {
      const errorMessage = 'File too large';

      vi.mocked(useMediaFileProcessor).mockReturnValue({
        error: errorMessage,
        processFiles: mockProcessFiles,
        clearError: vi.fn(),
      });

      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      const errorAlert = screen.getByTestId('basic-alert');
      expect(errorAlert).toHaveTextContent(errorMessage);
    });

    it('should handle file rejections', async () => {
      const rejectionError = 'Invalid file type';

      mockProcessFiles.mockResolvedValueOnce({
        mediaFiles: [],
        error: rejectionError,
      });

      vi.mocked(useMediaFileProcessor).mockReturnValue({
        error: rejectionError,
        processFiles: mockProcessFiles,
        clearError: vi.fn(),
      });

      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      const fileDropZone = screen.getByTestId('file-drop-zone');
      fireEvent.click(fileDropZone);

      expect(await screen.findByTestId('basic-alert')).toHaveTextContent(rejectionError);
    });

    it('should not show max size error when there is a processing error', async () => {
      const processingError = 'Processing failed';

      vi.mocked(useMediaFileProcessor).mockReturnValue({
        error: processingError,
        processFiles: mockProcessFiles,
        clearError: vi.fn(),
      });

      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      const alerts = screen.getAllByTestId('basic-alert');
      expect(alerts).toHaveLength(1);
      expect(alerts[0]).toHaveTextContent(processingError);
    });
  });

  describe('Integration with Custom Hooks', () => {
    it('should call useMediaFileProcessor hook', () => {
      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });
      expect(useMediaFileProcessor).toHaveBeenCalled();
    });

    it('should use cleanupFileByUrl from useMediaFileManager', async () => {
      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      // Add and then delete a file
      const fileDropZone = screen.getByTestId('file-drop-zone');
      fireEvent.click(fileDropZone);

      expect(await screen.findByTestId('bulk-media-preview')).toBeInTheDocument();

      const deleteButton = screen.getByTestId('delete-button');
      fireEvent.click(deleteButton);

      expect(vi.mocked(cleanupFileByUrl)).toHaveBeenCalledWith('blob:mock-url');
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty file arrays', async () => {
      mockProcessFiles.mockResolvedValueOnce({
        mediaFiles: [],
        error: undefined,
      });

      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      const fileDropZone = screen.getByTestId('file-drop-zone');
      fireEvent.click(fileDropZone);

      await waitFor(() => {
        expect(screen.queryByTestId('bulk-media-preview')).not.toBeInTheDocument();
        expect(screen.queryByTestId('dashboard-divider')).not.toBeInTheDocument();
      });
    });

    it('should handle processFiles rejection gracefully', async () => {
      // Instead of testing rejection, test that component handles empty results gracefully
      mockProcessFiles.mockResolvedValueOnce({
        mediaFiles: [],
        error: 'Processing failed',
      });

      renderWithProviders(<BulkMediaEditor {...defaultProps} />, { store: mockStore });

      const fileDropZone = screen.getByTestId('file-drop-zone');
      fireEvent.click(fileDropZone);

      await waitFor(() => {
        expect(mockProcessFiles).toHaveBeenCalled();
      });

      // Component should not crash and should remain in initial state
      expect(screen.queryByTestId('bulk-media-preview')).not.toBeInTheDocument();
    });
  });
});
