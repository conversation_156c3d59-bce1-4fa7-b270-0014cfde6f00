import { EditorTemplate } from '../EditorTemplate';
import { useMemo, useState } from 'react';
import { DashboardDivider } from '@g17eco/atoms/divider';
import { FileDropZone } from '@components/files/FileDropZone';
import { BulkMediaEditorProps } from '../../types';
import {
  GridDashboardMediaItemBase,
  InsightDashboardItemType,
  ToUploadMediaFile,
} from '@g17eco/types/insight-custom-dashboard';
import { DropZonePlaceholder } from './DropZonePlaceholder';
import { ACCEPT, MAX_SIZE } from '../../utils/media-utils';
import { FileRejection } from 'react-dropzone';
import { BasicAlert } from '@g17eco/molecules/alert';
import { generateObjectId } from '@utils/object-id';
import { BulkMediaPreview } from './BulkMediaPreview';
import { useMediaFileProcessor } from '../../hooks/useMediaFileProcessor';
import { cleanupFileByUrl } from '@utils/files';

/** Need to use tempId to keep track of the items as we don't have _id yet. */
type BulkMediaItemWithUniqueId = GridDashboardMediaItemBase & { files: ToUploadMediaFile[]; tempId: string };

export const BulkMediaEditor = ({ handleCancel, updateItem }: BulkMediaEditorProps) => {
  const [mediaItems, setMediaItems] = useState<BulkMediaItemWithUniqueId[]>([]);
  const { error, processFiles } = useMediaFileProcessor();

  const handleSubmit = () => updateItem(mediaItems);

  const handleFilesAdded = async (files: File[], fileRejections: FileRejection[]) => {
    const { mediaFiles } = await processFiles(files, fileRejections);

    const newMediaItems: BulkMediaItemWithUniqueId[] = (mediaFiles).map((file) => ({
      tempId: generateObjectId(),
      type: InsightDashboardItemType.Media,
      files: [file],
    }));
    setMediaItems((mediaItems) => [...mediaItems, ...newMediaItems]);
  };

  const handleClickDelete = (id: string, fileUrl: string) => {
    const updatedItems = mediaItems.filter((item) => item.tempId !== id);
    setMediaItems(updatedItems);
    cleanupFileByUrl(fileUrl);
  };

  const handleChangeTitle = (id: string, title: string) => {
    setMediaItems((mediaItems) => mediaItems.map((item) => (item.tempId === id ? { ...item, title } : item)));
  };

  const hasReachedMaxSize = useMemo(() => {
    return mediaItems.reduce((acc, item) => acc + item.files[0].originalFile.size, 0) > MAX_SIZE;
  }, [mediaItems]);

  const isDisabled = !mediaItems.length || hasReachedMaxSize;

  return (
    <EditorTemplate isDisabled={isDisabled} handleCancel={handleCancel} handleSubmit={handleSubmit}>
      <p className='mt-3 text-medium'>Upload media</p>
      <FileDropZone multiple maxSize={MAX_SIZE} accept={ACCEPT} onDrop={handleFilesAdded}>
        <DropZonePlaceholder />
      </FileDropZone>
      {mediaItems.length ? <DashboardDivider className='mt-4 mb-4' /> : null}
      {mediaItems.map((item) => (
        <BulkMediaPreview
          key={item.tempId}
          title={item.title}
          file={item.files[0]}
          handleClickDelete={() => handleClickDelete(item.tempId, item.files[0].url)}
          handleChangeTitle={(title) => handleChangeTitle(item.tempId, title)}
        />
      ))}
      {error ? (
        <BasicAlert type='danger' className='mt-2 text-break'>
          {error}
        </BasicAlert>
      ) : null}
      {!error && hasReachedMaxSize ? (
        <BasicAlert type='danger' className='mt-2 text-break'>
          Total size of files is larger than {MAX_SIZE} bytes
        </BasicAlert>
      ) : null}
    </EditorTemplate>
  );
};
