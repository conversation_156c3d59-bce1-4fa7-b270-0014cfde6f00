import { EditorTemplate } from '../EditorTemplate';
import { useMemo, useState } from 'react';
import { DashboardDivider } from '@g17eco/atoms/divider';
import { FileDropZone } from '@components/files/FileDropZone';
import { BulkMediaEditorProps, MediaItem } from '../../types';
import { InsightDashboardItemType, ToUploadMediaFile } from '@g17eco/types/insight-custom-dashboard';
import { DropZonePlaceholder } from './DropZonePlaceholder';
import { ACCEPT, MAX_SIZE, cloneFile, getFileRatio } from '../../utils/media-utils';
import { FileRejection } from 'react-dropzone';
import { BasicAlert } from '@g17eco/molecules/alert';
import { generateObjectId } from '@utils/object-id';
import { BulkMediaPreview } from './BulkMediaPreview';

/** Need to use uniqueId to keep track of the items as we don't have _id yet. */
type BulkMediaItemWithUniqueId = MediaItem & { files: ToUploadMediaFile[]; uniqueId: string };

export const BulkMediaEditor = ({ handleCancel, updateItem }: BulkMediaEditorProps) => {
  const [mediaItems, setMediaItems] = useState<BulkMediaItemWithUniqueId[]>([]);
  const [error, setError] = useState<string | undefined>();

  const handleSubmit = () => updateItem(mediaItems);

  const handleFilesAdded = async (files: File[], fileRejections: FileRejection[]) => {
    const mediaFiles: ToUploadMediaFile[] = await Promise.all(
      files.map(async (file) => {
        // We want to use local object here because user can cancel the editing.
        // We only upload the file later once user saves the dashboard.
        const url = URL.createObjectURL(file);
        const type = file.type;
        const normalizedName = file.name.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
        return {
          name: normalizedName,
          url,
          type,
          ratio: await getFileRatio({ url, type }),
          originalFile: cloneFile(file, normalizedName),
        };
      }),
    );

    const newMediaItems: BulkMediaItemWithUniqueId[] = mediaFiles.map((file) => ({
      uniqueId: generateObjectId(),
      type: InsightDashboardItemType.Media,
      files: [file],
    }));
    setMediaItems((mediaItems) => [...mediaItems, ...newMediaItems]);

    const errors = fileRejections.map(({ errors }) => errors.map(({ message }) => message)).flat();
    setError(errors.join(', '));
  };

  const handleClickDelete = (id: string, fileUrl: string) => {
    const updatedItems = mediaItems.filter((item) => item.uniqueId !== id);
    setMediaItems(updatedItems);
    // Release memory. https://developer.mozilla.org/en-US/docs/Web/API/URL/createObjectURL_static#memory_management
    URL.revokeObjectURL(fileUrl);
  };

  const handleChangeTitle = (id: string, title: string) => {
    setMediaItems((mediaItems) => mediaItems.map((item) => (item.uniqueId === id ? { ...item, title } : item)));
  };

  const hasReachedMaxSize = useMemo(() => {
    return mediaItems.reduce((acc, item) => acc + item.files[0].originalFile.size, 0) > MAX_SIZE;
  }, [mediaItems]);

  const isDisabled = !mediaItems.length || hasReachedMaxSize;

  return (
    <EditorTemplate isDisabled={isDisabled} handleCancel={handleCancel} handleSubmit={handleSubmit}>
      <p className='mt-3 text-medium'>Upload media</p>
      <FileDropZone multiple maxSize={MAX_SIZE} accept={ACCEPT} onDrop={handleFilesAdded}>
        <DropZonePlaceholder />
      </FileDropZone>
      {mediaItems.length ? <DashboardDivider className='mt-4 mb-4' /> : null}
      {mediaItems.map((item) => (
        <BulkMediaPreview
          key={item.uniqueId}
          title={item.title}
          file={item.files[0]}
          handleClickDelete={() => handleClickDelete(item.uniqueId, item.files[0].url)}
          handleChangeTitle={(title) => handleChangeTitle(item.uniqueId, title)}
        />
      ))}
      {error ? (
        <BasicAlert type='danger' className='mt-2 text-break'>
          {error}
        </BasicAlert>
      ) : null}
      {!error && hasReachedMaxSize ? (
        <BasicAlert type='danger' className='mt-2 text-break'>
          Total size of files is larger than {MAX_SIZE} bytes
        </BasicAlert>
      ) : null}
    </EditorTemplate>
  );
};
