import IconButton from '@components/button/IconButton';
import { checkIsVideo } from '../../utils/dashboard-utils';
import { ToUploadMediaFile } from '@g17eco/types/insight-custom-dashboard';
import { fileDownload } from '@utils/files';
import NumberFormat from '@utils/number-format';
import { Input } from 'reactstrap';
import './BulkMediaPreview.scss';
import { MAX_LENGTH_TITLE } from '../constant';

export interface BulkMediaPreviewProps {
  title: string | undefined;
  file: ToUploadMediaFile;
  handleClickDelete: () => void;
  handleChangeTitle: (title: string) => void;
}

export const BulkMediaPreview = ({ title, file, handleClickDelete, handleChangeTitle }: BulkMediaPreviewProps) => {
  const isVideo = checkIsVideo(file);
  return (
    <div className='d-flex align-self-center gap-1 media-tile mt-3'>
      <IconButton className='icon-button--size-xs' size='xs' icon='fal fa-xmark' onClick={handleClickDelete} />
      <div className={`d-flex ${isVideo ? 'flex-column' : 'flex-row'} flex-grow-1 gap-1`}>
        {isVideo ? (
          <div
            className='text-ThemeIconSecondary text-truncate h-100'
            style={{ cursor: 'pointer', maxWidth: '300px' }}
            onClick={() => fileDownload(file.url)}
          >
            {file.name} (<NumberFormat value={file.originalFile.size / 1024} decimalPlaces={1} suffix='kb' />)
          </div>
        ) : (
          <div className='img-thumbnail'>
            <img
              onClick={() => fileDownload(file.url)}
              key={file.name}
              src={file.url}
              alt={file.name}
              className='media-tile--image rounded-1'
            />
            <div>
              <NumberFormat value={file.originalFile.size / 1024} decimalPlaces={1} suffix='kb' />
            </div>
          </div>
        )}
        <div className='d-flex h-100 gap-1 align-items-start flex-grow-1'>
          <Input
            type='text'
            className='text-md'
            maxLength={MAX_LENGTH_TITLE}
            value={title}
            onChange={(e) => handleChangeTitle(e.target.value)}
            placeholder='Caption (optional)'
            bsSize='sm'
          />
        </div>
      </div>
    </div>
  );
};
