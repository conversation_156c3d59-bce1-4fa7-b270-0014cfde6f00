import { EditorTemplate } from '../EditorTemplate';
import { Input } from 'reactstrap';
import { useState } from 'react';
import { DashboardDivider } from '@g17eco/atoms/divider';
import { FileDropZone } from '@components/files/FileDropZone';
import { MediaEditorProps, MediaItem } from '../../types';
import { InsightDashboardItemType, MediaData, MediaFile } from '@g17eco/types/insight-custom-dashboard';
import { Preview } from './Preview';
import { DropZonePlaceholder } from './DropZonePlaceholder';
import { ACCEPT, MAX_SIZE } from '@features/custom-dashboard/utils/media-utils';
import { FileRejection } from 'react-dropzone';
import { BasicAlert } from '@g17eco/molecules/alert';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { MAX_LENGTH_TITLE } from '../constant';
import { useMediaFileProcessor } from '../../hooks/useMediaFileProcessor';
import { useMediaFileManager } from '../../hooks/useMediaFileManager';

const TITLE_TOOLTIP = 'Title will be shown under the media';

const getMediaData = ({ title = '', files = [] }: MediaItem) => ({ title, files });

export const MediaEditor = ({ handleCancel, updateItem, editingItem }: MediaEditorProps) => {
  const [media, setMedia] = useState<MediaData>(getMediaData(editingItem));
  const { error, processFiles } = useMediaFileProcessor();
  const { removeFileFromArray } = useMediaFileManager();

  const handleChangeMedia = (newMediaData: Partial<MediaData>) => {
    setMedia((mediaData) => ({ ...mediaData, ...newMediaData }));
  };

  const handleSubmit = async () => {
    updateItem({ ...editingItem, ...media, type: InsightDashboardItemType.Media });
  };

  const handleFilesAdded = async (files: File[], fileRejections: FileRejection[]) => {
    const { mediaFiles } = await processFiles(files, fileRejections);
    handleChangeMedia({ files: mediaFiles });
  };

  const handleClickDelete = (file: MediaFile) => {
    const updatedFiles = removeFileFromArray(media.files, file);
    handleChangeMedia({ files: updatedFiles });
  };

  return (
    <EditorTemplate isDisabled={!media.files.length} handleCancel={handleCancel} handleSubmit={handleSubmit}>
      <p className='mt-3 text-medium'>
        Title (optional)
        <SimpleTooltip text={TITLE_TOOLTIP} className='text-ThemeIconSecondary'>
          <i className='fal fa-info-circle ml-2' />
        </SimpleTooltip>
      </p>
      <Input
        type='text'
        className='text-md'
        maxLength={MAX_LENGTH_TITLE}
        value={media.title}
        onChange={(e) => handleChangeMedia({ title: e.target.value })}
        placeholder='Enter title'
      />
      <DashboardDivider className='mt-4 mb-4' />
      <p className='mt-3 text-medium'>Upload media</p>
      {media.files.length ? (
        <Preview files={media.files} handleClickDelete={handleClickDelete} />
      ) : (
        <FileDropZone multiple={false} maxSize={MAX_SIZE} accept={ACCEPT} onDrop={handleFilesAdded}>
          <DropZonePlaceholder />
        </FileDropZone>
      )}
      {error ? (
        <BasicAlert type='danger' className='mt-2 text-break'>
          {error}
        </BasicAlert>
      ) : null}
    </EditorTemplate>
  );
};
