import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { FileRejection } from 'react-dropzone';
import { useMediaFileProcessor } from './useMediaFileProcessor';
import * as mediaUtils from '../utils/media-utils';

vi.mock('../utils/media-utils', () => ({
  cloneFile: vi.fn(),
  getFileRatio: vi.fn(),
}));

const mockCreateObjectURL = vi.fn();

describe('useMediaFileProcessor', () => {
  const mockCloneFile = vi.mocked(mediaUtils.cloneFile);
  const mockGetFileRatio = vi.mocked(mediaUtils.getFileRatio);

  beforeEach(() => {
    global.URL.createObjectURL = mockCreateObjectURL;
    mockCreateObjectURL.mockReturnValue('mock-url');
    mockGetFileRatio.mockResolvedValue(1.5);
    mockCloneFile.mockImplementation((file, name) => new File([file], name, { type: file.type }));
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should initialize with no error', () => {
    const { result } = renderHook(() => useMediaFileProcessor());

    expect(result.current.error).toBeUndefined();
    expect(typeof result.current.processFiles).toBe('function');
    expect(typeof result.current.clearError).toBe('function');
  });

  it('should process files successfully', async () => {
    const { result } = renderHook(() => useMediaFileProcessor());

    const mockFile = new File(['content'], 'test file.jpg', { type: 'image/jpeg' });
    const files = [mockFile];
    const fileRejections: FileRejection[] = [];

    let processResult;
    await act(async () => {
      processResult = await result.current.processFiles(files, fileRejections);
    });

    expect(processResult).toBeDefined();
    expect(processResult!.mediaFiles).toHaveLength(1);
    expect(processResult!.error).toBeUndefined();

    const processedFile = processResult!.mediaFiles[0];
    expect(processedFile).toEqual({
      name: 'test file.jpg', // Normalized name (no diacritics in this case)
      url: 'mock-url',
      type: 'image/jpeg',
      ratio: 1.5,
      originalFile: expect.any(File),
    });

    expect(mockCreateObjectURL).toHaveBeenCalledWith(mockFile);
    expect(mockGetFileRatio).toHaveBeenCalledWith({ url: 'mock-url', type: 'image/jpeg' });
    expect(mockCloneFile).toHaveBeenCalledWith(mockFile, 'test file.jpg');
  });

  it('should normalize file names by removing diacritics', async () => {
    const { result } = renderHook(() => useMediaFileProcessor());

    const mockFile = new File(['content'], 'tëst fîlé.jpg', { type: 'image/jpeg' });
    const files = [mockFile];
    const fileRejections: FileRejection[] = [];

    let processResult;
    await act(async () => {
      processResult = await result.current.processFiles(files, fileRejections);
    });

    const processedFile = processResult!.mediaFiles[0];
    expect(processedFile.name).toBe('test file.jpg'); // Diacritics removed
    expect(mockCloneFile).toHaveBeenCalledWith(mockFile, 'test file.jpg');
  });

  it('should handle file rejections and set error', async () => {
    const { result } = renderHook(() => useMediaFileProcessor());

    const files: File[] = [];
    const fileRejections: FileRejection[] = [
      {
        file: new File([''], 'large-file.jpg'),
        errors: [
          { code: 'file-too-large', message: 'File is too large' },
          { code: 'file-invalid-type', message: 'Invalid file type' },
        ],
      },
      {
        file: new File([''], 'another-file.jpg'),
        errors: [{ code: 'too-many-files', message: 'Too many files' }],
      },
    ];

    let processResult;
    await act(async () => {
      processResult = await result.current.processFiles(files, fileRejections);
    });

    expect(processResult!.mediaFiles).toHaveLength(0);
    expect(processResult!.error).toBe('File is too large, Invalid file type, Too many files');
    expect(result.current.error).toBe('File is too large, Invalid file type, Too many files');
  });

  it('should process multiple files correctly', async () => {
    const { result } = renderHook(() => useMediaFileProcessor());

    const mockFile1 = new File(['content1'], 'file1.jpg', { type: 'image/jpeg' });
    const mockFile2 = new File(['content2'], 'file2.png', { type: 'image/png' });
    const files = [mockFile1, mockFile2];
    const fileRejections: FileRejection[] = [];

    mockCreateObjectURL.mockReturnValueOnce('mock-url-1').mockReturnValueOnce('mock-url-2');
    mockGetFileRatio.mockResolvedValueOnce(1.2).mockResolvedValueOnce(1.8);

    let processResult;
    await act(async () => {
      processResult = await result.current.processFiles(files, fileRejections);
    });

    expect(processResult!.mediaFiles).toHaveLength(2);
    expect(processResult!.mediaFiles[0].name).toBe('file1.jpg');
    expect(processResult!.mediaFiles[0].url).toBe('mock-url-1');
    expect(processResult!.mediaFiles[0].ratio).toBe(1.2);
    expect(processResult!.mediaFiles[1].name).toBe('file2.png');
    expect(processResult!.mediaFiles[1].url).toBe('mock-url-2');
    expect(processResult!.mediaFiles[1].ratio).toBe(1.8);
  });

  it('should clear error when clearError is called', async () => {
    const { result } = renderHook(() => useMediaFileProcessor());

    // First, set an error
    const fileRejections: FileRejection[] = [
      {
        file: new File([''], 'test.jpg'),
        errors: [{ code: 'file-too-large', message: 'File is too large' }],
      },
    ];

    await act(async () => {
      await result.current.processFiles([], fileRejections);
    });

    expect(result.current.error).toBe('File is too large');

    // Then clear the error
    act(() => {
      result.current.clearError();
    });

    expect(result.current.error).toBeUndefined();
  });

  it('should handle empty files and rejections', async () => {
    const { result } = renderHook(() => useMediaFileProcessor());

    let processResult;
    await act(async () => {
      processResult = await result.current.processFiles([], []);
    });

    expect(processResult!.mediaFiles).toHaveLength(0);
    expect(processResult!.error).toBeUndefined();
    expect(result.current.error).toBeUndefined();
  });
});
