import { MediaFile } from '@g17eco/types/insight-custom-dashboard';

/**
 * Custom hook that abstracts common file management operations including
 * file deletion with proper URL cleanup for memory management.
 */
export const useMediaFileManager = () => {
  /**
   * Cleans up the object URL to prevent memory leaks.
   */
  const cleanupFileByUrl = (fileUrl: string) => {
    // Release memory. https://developer.mozilla.org/en-US/docs/Web/API/URL/createObjectURL_static#memory_management
    URL.revokeObjectURL(fileUrl);
  };

  /**
   * Removes a file from an array of files and cleans up its URL.
   * Returns the filtered array without the specified file.
   */
  const removeFileFromArray = (files: MediaFile[], fileToRemove: MediaFile): MediaFile[] => {
    cleanupFileByUrl(fileToRemove.url);
    return files.filter((f) => f !== fileToRemove);
  };

  return {
    cleanupFileByUrl,
    removeFileFromArray,
  };
};
