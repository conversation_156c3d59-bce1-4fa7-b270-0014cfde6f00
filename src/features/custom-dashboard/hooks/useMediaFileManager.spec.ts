import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useMediaFileManager } from './useMediaFileManager';
import { MediaFile } from '@g17eco/types/insight-custom-dashboard';

const mockRevokeObjectURL = vi.fn();

describe('useMediaFileManager', () => {
  beforeEach(() => {
    global.URL.revokeObjectURL = mockRevokeObjectURL;
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should initialize with all expected functions', () => {
    const { result } = renderHook(() => useMediaFileManager());

    expect(typeof result.current.cleanupFileByUrl).toBe('function');
    expect(typeof result.current.removeFileFromArray).toBe('function');
  });

  describe('cleanupFileByUrl', () => {
    it('should revoke object URL when deleting by URL string', () => {
      const { result } = renderHook(() => useMediaFileManager());

      const fileUrl = 'blob:http://localhost/test-url';

      act(() => {
        result.current.cleanupFileByUrl(fileUrl);
      });

      expect(mockRevokeObjectURL).toHaveBeenCalledWith(fileUrl);
      expect(mockRevokeObjectURL).toHaveBeenCalledTimes(1);
    });
  });

  describe('removeFileFromArray', () => {
    it('should remove file from array and clean up URL', () => {
      const { result } = renderHook(() => useMediaFileManager());

      const file1: MediaFile = {
        name: 'file1.jpg',
        url: 'blob:http://localhost/file1-url',
        type: 'image/jpeg',
        documentId: 'doc-1',
        ratio: 1.5,
      };

      const file2: MediaFile = {
        name: 'file2.png',
        url: 'blob:http://localhost/file2-url',
        type: 'image/png',
        documentId: 'doc-2',
        ratio: 1.2,
      };

      const file3: MediaFile = {
        name: 'file3.gif',
        url: 'blob:http://localhost/file3-url',
        type: 'image/gif',
        documentId: 'doc-3',
        ratio: 1.0,
      };

      const files = [file1, file2, file3];

      let updatedFiles;
      act(() => {
        updatedFiles = result.current.removeFileFromArray(files, file2);
      });

      expect(updatedFiles).toEqual([file1, file3]);
      expect(updatedFiles).toHaveLength(2);
      expect(mockRevokeObjectURL).toHaveBeenCalledWith('blob:http://localhost/file2-url');
      expect(mockRevokeObjectURL).toHaveBeenCalledTimes(1);
    });

    it('should return original array if file to remove is not found', () => {
      const { result } = renderHook(() => useMediaFileManager());

      const file1: MediaFile = {
        name: 'file1.jpg',
        url: 'blob:http://localhost/file1-url',
        type: 'image/jpeg',
        documentId: 'doc-1',
        ratio: 1.5,
      };

      const file2: MediaFile = {
        name: 'file2.png',
        url: 'blob:http://localhost/file2-url',
        type: 'image/png',
        documentId: 'doc-2',
        ratio: 1.2,
      };

      const fileNotInArray: MediaFile = {
        name: 'file3.gif',
        url: 'blob:http://localhost/file3-url',
        type: 'image/gif',
        documentId: 'doc-3',
        ratio: 1.0,
      };

      const files = [file1, file2];

      let updatedFiles;
      act(() => {
        updatedFiles = result.current.removeFileFromArray(files, fileNotInArray);
      });

      expect(updatedFiles).toEqual([file1, file2]);
      expect(updatedFiles).toHaveLength(2);
      expect(mockRevokeObjectURL).toHaveBeenCalledWith('blob:http://localhost/file3-url');
      expect(mockRevokeObjectURL).toHaveBeenCalledTimes(1);
    });

    it('should handle empty array', () => {
      const { result } = renderHook(() => useMediaFileManager());

      const fileToRemove: MediaFile = {
        name: 'file.jpg',
        url: 'blob:http://localhost/file-url',
        type: 'image/jpeg',
        documentId: 'doc-1',
        ratio: 1.5,
      };

      const files: MediaFile[] = [];

      let updatedFiles;
      act(() => {
        updatedFiles = result.current.removeFileFromArray(files, fileToRemove);
      });

      expect(updatedFiles).toEqual([]);
      expect(updatedFiles).toHaveLength(0);
      expect(mockRevokeObjectURL).toHaveBeenCalledWith('blob:http://localhost/file-url');
      expect(mockRevokeObjectURL).toHaveBeenCalledTimes(1);
    });
  });
});
