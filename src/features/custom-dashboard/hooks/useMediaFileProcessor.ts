import { useState } from 'react';
import { FileRejection } from 'react-dropzone';
import { ToUploadMediaFile } from '@g17eco/types/insight-custom-dashboard';
import { cloneFile, getFileRatio } from '../utils/media-utils';

export interface ProcessedFileResult {
  mediaFiles: ToUploadMediaFile[];
  error: string | undefined;
}

/**
 * Custom hook that abstracts the common file processing logic used by MediaEditor and BulkMediaEditor.
 * Handles file normalization, URL creation, ratio calculation, and error processing from file rejections.
 */
export const useMediaFileProcessor = () => {
  const [error, setError] = useState<string | undefined>();

  const processFiles = async (files: File[], fileRejections: FileRejection[]): Promise<ProcessedFileResult> => {
    // Process accepted files
    const mediaFiles: ToUploadMediaFile[] = await Promise.all(
      files.map(async (file) => {
        // We want to use local object here because user can cancel the editing.
        // We only upload the file later once user saves the dashboard.
        const url = URL.createObjectURL(file);
        const type = file.type;
        const normalizedName = file.name.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
        return {
          name: normalizedName,
          url,
          type,
          ratio: await getFileRatio({ url, type }),
          originalFile: cloneFile(file, normalizedName),
        };
      }),
    );

    // Process file rejections and extract error messages
    const errors = fileRejections.map(({ errors }) => errors.map(({ message }) => message)).flat();
    const errorMessage = errors.length > 0 ? errors.join(', ') : undefined;
    setError(errorMessage);

    return {
      mediaFiles,
      error: errorMessage,
    };
  };

  const clearError = () => {
    setError(undefined);
  };

  return {
    error,
    processFiles,
    clearError,
  };
};
