/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React from 'react';
import {
  DropzoneOptions,
  useDropzone
} from 'react-dropzone';
import './FileDropZone.scss';

const defaultBody = (
  <span>
    <i className='fa fa-file mr-3' />Attach files by dragging and dropping or <u>selecting them</u>
  </span>
);

export interface FileDropZoneProps extends DropzoneOptions {
  disabled?: boolean;
  className?: string;
  multiple?: boolean;
  children?: JSX.Element;
}

export const FileDropZone = (props: FileDropZoneProps) => {
  const { className, children } = props;
  const { getRootProps, getInputProps, isDragActive } = useDropzone(props);

  return (
    <span {...getRootProps({ className: `FileDropZone dropzone ${className || ''} ${isDragActive ? 'drop-active' : ''}` })}>
      <input data-testid='file-dropzone-input' {...getInputProps()} />
      {children || defaultBody}
    </span>
  );
};
